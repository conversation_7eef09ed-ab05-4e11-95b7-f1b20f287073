{"name": "vidmob-internal-bff", "version": "0.0.0-SNAPSHOT", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@automapper/classes": "8.8.1", "@automapper/core": "8.8.1", "@automapper/nestjs": "8.8.1", "@automapper/types": "6.3.1", "@aws-sdk/client-s3": "^3.662.0", "@aws-sdk/s3-request-presigner": "^3.662.0", "@nestjs/axios": "3.1.3", "@nestjs/common": "10.4.18", "@nestjs/config": "3.3.0", "@nestjs/core": "10.4.18", "@nestjs/platform-express": "10.4.18", "@nestjs/swagger": "8.1.1", "@nestjs/typeorm": "10.0.2", "@vidmob/vidmob-nestjs-common": "2.0.1", "@vidmob/vidmob-organization-service-sdk": "0.0.0-SNAPSHOT-8e571278-20250620184045", "@vidmob/vidmob-soa-analytics-service-sdk": "0.0.0-SNAPSHOT-bd912fa6-20250630193527", "@vidmob/vidmob-soa-scoring-service-sdk": "0.0.0-SNAPSHOT-dd20ecff-20250707152653", "axios": "1.6.7", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "helmet": "^8.1.0", "md5": "2.3.0", "mongodb": "^5.9.2", "mysql2": "3.10.3", "rxjs": "^7.8.1", "typeorm": "0.3.20", "zod": "^3.25.48"}, "devDependencies": {"@nestjs/cli": "^10.4.9", "@nestjs/schematics": "^10.2.3", "@nestjs/testing": "^10.4.18", "@types/express": "^5.0.0", "@types/helmet": "^0.0.48", "@types/jest": "^29.5.2", "@types/node": "18.15.11", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^5.59.7", "@typescript-eslint/parser": "^5.59.7", "eslint": "^8.41.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.6"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}