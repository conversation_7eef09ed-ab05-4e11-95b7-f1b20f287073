import '@vidmob/vidmob-nestjs-common/dist/tracing';

import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { SwaggerModule } from '@nestjs/swagger';
import {
  DEFAULT_PORT,
  RookoutService,
  VidmobCommonModule,
} from '@vidmob/vidmob-nestjs-common';
import { ValidationPipe, VersioningType } from '@nestjs/common';
import { getSwaggerDoc } from './swagger';
import { ConfigService } from '@nestjs/config';
import { CONFIG_PORT } from './common/constants/configuration.constants';
import { SERVICE_NAME } from './common/constants/api.constants';

const SWAGGER_URI = 'docs';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  VidmobCommonModule.setupLogger(app); // Setup logger (uses <PERSON><PERSON> Logger)
  app.enableShutdownHooks();

  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
    }),
  );

  app.enableVersioning({
    type: VersioningType.URI,
    defaultVersion: '1',
  });

  const document = getSwaggerDoc(app);
  SwaggerModule.setup(SWAGGER_URI, app, document);

  const configService = app.get(ConfigService);
  const environment = configService.get('NODE_ENV');
  const rookout = app.get(RookoutService);
  await rookout.init(environment, { service: SERVICE_NAME });
  const port = configService.get<number>(CONFIG_PORT, DEFAULT_PORT);
  console.log('Listening on port: ' + port);

  await app.listen(port);
}
bootstrap();
