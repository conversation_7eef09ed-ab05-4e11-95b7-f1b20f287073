import '@vidmob/vidmob-nestjs-common/dist/tracing';

import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { SwaggerModule } from '@nestjs/swagger';
import {
  DEFAULT_PORT,
  RookoutService,
  VidmobCommonModule,
} from '@vidmob/vidmob-nestjs-common';
import { ValidationPipe, VersioningType } from '@nestjs/common';
import { getSwaggerDoc } from './swagger';
import { ConfigService } from '@nestjs/config';
import {
  CONFIG_CORS_ORIGIN,
  CONFIG_ENV_LOCAL,
  CONFIG_ENV_SDK,
  CONFIG_PORT,
  CORS_ALLOW_HEADERS,
} from './common/constants/configuration.constants';
import { SERVICE_NAME } from './common/constants/api.constants';
import { CorsOptions } from '@nestjs/common/interfaces/external/cors-options.interface';

const SWAGGER_URI = 'docs';
const CORS_ALLOW_ORIGIN_ALL = '*';
const CORS_DEFAULT_ALLOW_HEADERS = ['Content-Type', 'Authorization'];

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  VidmobCommonModule.setupLogger(app); // Setup logger (uses Pino Logger)
  app.enableShutdownHooks();

  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
    }),
  );

  app.enableVersioning({
    type: VersioningType.URI,
    defaultVersion: '1',
  });

  const configService = app.get(ConfigService);
  const environment = configService.get('NODE_ENV');
  const rookout = app.get(RookoutService);
  await rookout.init(environment, { service: SERVICE_NAME });

  if (environment === CONFIG_ENV_SDK || environment === CONFIG_ENV_LOCAL) {
    const document = getSwaggerDoc(app);
    SwaggerModule.setup(SWAGGER_URI, app, document);
  }

  const port = configService.get<number>(CONFIG_PORT, DEFAULT_PORT);
  console.log('Listening on port: ' + port);

  const allowOriginList = configService.get<string[]>(CONFIG_CORS_ORIGIN, [
    CORS_ALLOW_ORIGIN_ALL,
  ]);
  const allowHeaderList = configService.get<string[]>(
    CORS_ALLOW_HEADERS,
    CORS_DEFAULT_ALLOW_HEADERS,
  );

  /*
   * This is a custom CORS implementation that allows us to use a whitelist
   */
  app.enableCors((req: any, callback: any) => {
    let corsOptions = { allowedHeaders: allowHeaderList } as CorsOptions;
    const originHeader = req.header('Origin');

    // log all CORS requests
    console.log(`CORS request from: ${originHeader}`);
    console.log(`CORS allow list: ${allowOriginList}`);
    console.log(`CORS allow headers: ${allowHeaderList}`);

    if (allowOriginList.indexOf(CORS_ALLOW_ORIGIN_ALL) !== -1) {
      return callback(null, { origin: CORS_ALLOW_ORIGIN_ALL });
    }

    if (allowOriginList.indexOf(originHeader) !== -1) {
      corsOptions = { origin: originHeader, ...corsOptions };
    } else {
      corsOptions = { origin: undefined, ...corsOptions };
    }
    callback(null, corsOptions);
  });

  await app.listen(port);
}
bootstrap();
