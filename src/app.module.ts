import { ClassSerializerInterceptor, Module } from '@nestjs/common';
import {
  configuration,
  databaseProvider,
  HealthModule,
  RookoutModule,
  VidmobCommonModule,
  VidmobResponseInterceptorFactory,
} from '@vidmob/vidmob-nestjs-common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AutomapperModule } from '@automapper/nestjs';
import { TypeOrmModule } from '@nestjs/typeorm';
import { classes } from '@automapper/classes';
import { APP_INTERCEPTOR } from '@nestjs/core';
import {
  DEFAULT_RESPONSE_DESCRIPTION,
  DEFAULT_RESPONSE_ERROR_TYPE,
  INTERNAL_BFF_API_TAG_NAME,
} from './common/constants/api.constants';
import {
  ApiModule as AnalyticsApiModule,
  Configuration as AnalyticsConfiguration,
  ConfigurationParameters as AnalyticsConfigurationParameters,
} from '@vidmob/vidmob-soa-analytics-service-sdk';
import {
  ApiModule as OrganizationApiModule,
  Configuration as OrganizationConfiguration,
  ConfigurationParameters as OrganizationConfigurationParameters,
} from '@vidmob/vidmob-organization-service-sdk';
import {
  ApiModule as ScoringApiModule,
  Configuration as ScoringConfiguration,
  ConfigurationParameters as ScoringConfigurationParameters,
} from '@vidmob/vidmob-soa-scoring-service-sdk';
import { CriteriaPerformanceRetoolModule } from './criteria-performance-retool/criteria-performance-retool.module';
import { AppController } from './app.controller';
import { AppService } from './app.service';

@Module({
  imports: [
    ConfigModule.forRoot({
      load: [configuration],
      isGlobal: true,
    }),
    AutomapperModule.forRoot({ strategyInitializer: classes() }),
    VidmobCommonModule,
    HealthModule,
    TypeOrmModule.forRootAsync(databaseProvider()),
    RookoutModule,
    CriteriaPerformanceRetoolModule,
    AnalyticsApiModule.forRoot(
      (configService: ConfigService) => {
        const serviceConfig =
          configService.get<AnalyticsConfigurationParameters>(
            'analyticsService',
          );
        return new AnalyticsConfiguration(serviceConfig);
      },
      [ConfigService],
    ),
    OrganizationApiModule.forRoot(
      (configService: ConfigService) => {
        const serviceConfig =
          configService.get<OrganizationConfigurationParameters>(
            'organizationService',
          );
        return new OrganizationConfiguration(serviceConfig);
      },
      [ConfigService],
    ),
    ScoringApiModule.forRoot(
      (configService: ConfigService) => {
        const serviceConfig =
          configService.get<ScoringConfigurationParameters>('scoringService');
        return new ScoringConfiguration(serviceConfig);
      },
      [ConfigService],
    ),
  ],
  controllers: [AppController],
  providers: [
    AppService,
    { provide: APP_INTERCEPTOR, useClass: ClassSerializerInterceptor },
    {
      provide: APP_INTERCEPTOR,
      useValue: new VidmobResponseInterceptorFactory().create({
        errorConfiguration: {
          identifierPrefix: INTERNAL_BFF_API_TAG_NAME,
          serviceSystem: INTERNAL_BFF_API_TAG_NAME,
          defaultErrorMessage: DEFAULT_RESPONSE_DESCRIPTION,
          defaultErrorType: DEFAULT_RESPONSE_ERROR_TYPE,
        },
      }),
    },
  ],
})
export class AppModule {}
