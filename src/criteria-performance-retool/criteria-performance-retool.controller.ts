import { Controller, Get } from '@nestjs/common';
import { ApiSecurity, ApiTags } from '@nestjs/swagger';
import { CriteriaPerformanceRetoolService } from './criteria-performance-retool.service';

@ApiTags('Criteria Performance Retool')
@ApiSecurity('Bearer Token')
@Controller('criteria-performance-retool')
export class CriteriaPerformanceRetoolController {
  constructor(
    private readonly criteriaPerformanceRetoolService: CriteriaPerformanceRetoolService,
  ) {}

  @Get()
  getHello(): string {
    return this.criteriaPerformanceRetoolService.getHello();
  }
}
