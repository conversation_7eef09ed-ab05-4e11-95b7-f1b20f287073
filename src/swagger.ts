import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import {
  API_DESCRIPTION,
  API_TITLE,
  API_VERSION,
} from './common/constants/api.constants';
import { INestApplication } from '@nestjs/common';
import {
  ErrorResponse,
  PaginatedSuccessResponse,
  SuccessResponse,
} from '@vidmob/vidmob-nestjs-common';

export const getSwaggerDoc = (app: INestApplication) => {
  const config = new DocumentBuilder()
    .setTitle(API_TITLE)
    .setDescription(API_DESCRIPTION)
    .setVersion(API_VERSION)
    .addServer('https://internal-bff-dev.vidmob.com', 'Dev')
    .addServer('https://internal-bff-stage.vidmob.com', 'Stage')
    .addServer('https://internal-bff.vidmob.com', 'Prod')
    .addServer('http://localhost:3000', 'Local')
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'Bearer Token',
        name: 'Authorization',
        description: 'Enter your Bearer Token (without "Bearer " prefix)',
        in: 'header',
      },
      'Bearer Token', // This is the key that will be used to reference this auth method
    )
    .build();
  return SwaggerModule.createDocument(app, config, {
    extraModels: [SuccessResponse, PaginatedSuccessResponse, ErrorResponse],
    operationIdFactory: (_controllerKey: string, methodKey: string) =>
      methodKey,
  });
};
