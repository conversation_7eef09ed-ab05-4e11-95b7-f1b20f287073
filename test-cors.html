<!DOCTYPE html>
<html>
<head>
    <title>CORS Test</title>
</head>
<body>
    <h1>CORS Test - Simulating Swagger UI</h1>
    <button onclick="testCORS()">Test API Call (Should Fail with CORS)</button>
    <button onclick="testCORSWithCredentials()">Test API Call with Credentials</button>
    <div id="result"></div>

    <script>
        async function testCORS() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing CORS...';
            
            try {
                const response = await fetch('http://localhost:3001/v1/health', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                        'Origin': 'http://localhost:3001'
                    }
                });
                
                const data = await response.text();
                resultDiv.innerHTML = `<div style="color: green;">SUCCESS: ${data}</div>`;
            } catch (error) {
                resultDiv.innerHTML = `<div style="color: red;">CORS ERROR: ${error.message}</div>`;
                console.error('CORS Error:', error);
            }
        }

        async function testCORSWithCredentials() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing CORS with credentials...';
            
            try {
                const response = await fetch('http://localhost:3001/v1/health', {
                    method: 'GET',
                    credentials: 'include',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                        'Origin': 'http://localhost:3001'
                    }
                });
                
                const data = await response.text();
                resultDiv.innerHTML = `<div style="color: green;">SUCCESS: ${data}</div>`;
            } catch (error) {
                resultDiv.innerHTML = `<div style="color: red;">CORS ERROR: ${error.message}</div>`;
                console.error('CORS Error:', error);
            }
        }

        // Also test with curl-like approach
        function testWithXHR() {
            const xhr = new XMLHttpRequest();
            xhr.open('GET', 'http://localhost:3001/v1/health');
            xhr.setRequestHeader('Accept', 'application/json');
            xhr.setRequestHeader('Origin', 'http://localhost:3001');
            
            xhr.onload = function() {
                document.getElementById('result').innerHTML = `<div style="color: green;">XHR SUCCESS: ${xhr.responseText}</div>`;
            };
            
            xhr.onerror = function() {
                document.getElementById('result').innerHTML = `<div style="color: red;">XHR CORS ERROR</div>`;
            };
            
            xhr.send();
        }
    </script>

    <br><br>
    <button onclick="testWithXHR()">Test with XMLHttpRequest</button>
    
    <h2>Instructions:</h2>
    <ol>
        <li>Start your API server on port 3001: <code>PORT=3001 npm run start:dev</code></li>
        <li>Serve this HTML file on port 3001: <code>python3 -m http.server 3001</code></li>
        <li>Open <code>http://localhost:3001/test-cors.html</code></li>
        <li>Click the buttons to test CORS - they should fail!</li>
    </ol>
</body>
</html>
