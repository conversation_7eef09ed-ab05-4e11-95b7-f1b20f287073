# Copy this file to `config.local.yaml` and update the values below if necessary.
NODE_ENV: local
AWS_REGION: us-east-1

PORT: 3000

CORS_ORIGIN:
  [
    'https://acs-dev.vidmob.com',
    'https://acs-stage.vidmob.com',
    'https://acs.vidmob.com',
    'https://retool-dev.vidmob.com',
    'https://retool-prod.vidmob.com',
    'http://localhost:8063',
    'http://localhost:8064',
    'http://localhost:3009',
    'https://api-docs.vidmob.com',
    # 'http://localhost:3000',  # Comment this out to test CORS failure
  ]

analyticsService:
  # basePath: 'http://127.0.0.1:3002'  # Local
  basePath: 'https://soa-internal-dev.vidmob.com'
  defaultHeaders:
    vidmob-nestjs-service: 'vidmob-soa-analytics-service'
organizationService:
  # basePath: 'http://127.0.0.1:3003'  # Local
  basePath: 'https://soa-internal-dev.vidmob.com'
  defaultHeaders:
    vidmob-nestjs-service: 'vidmob-organization-service'
scoringService:
  # basePath: 'http://127.0.0.1:3004'  # Local
  basePath: 'https://soa-internal-dev.vidmob.com'
  defaultHeaders:
    vidmob-nestjs-service: 'vidmob-soa-scoring-service'

legacyAnalyticsServiceUrl: 'https://api-analytics-dev.vidmob.com'
legacyApiGwUrl: 'https://api-analytics-gw-dev.vidmob.com'
serviceUrl: 'https://api-public-dev.vidmob.com'
baseVidMobApiUrl: 'https://api-public-dev.vidmob.com/VidMob'

database:
  default:
    value:
      host: 127.0.0.1
      port: 13326
      database: vidmob
      username: root
      password: root
      ssl: false
