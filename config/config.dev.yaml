NODE_ENV: dev
AWS_REGION: us-east-1

# CORS_ORIGIN:
#   [
#     'https://acs-dev.vidmob.com',
#     'https://acs-stage.vidmob.com',
#     'https://acs.vidmob.com',
#     'https://retool-dev.vidmob.com',
#     'https://retool-prod.vidmob.com',
#     'http://localhost:8063',
#     'http://localhost:8064',
#     'http://localhost:3000',
#     'http://localhost:3009',
#     'https://api-docs.vidmob.com',
#   ]
# CORS_ALLOW_HEADERS:
#   [
#     'Accept',
#     'Accept-Version',
#     'Content-Type',
#     'Api-Version',
#     'Origin',
#     'X-Requested-With',
#     'Authorization',
#     'x-vidmob-user-id',
#     'x-vidmob-organization-id',
#     'x-vidmob-organization-ids',
#     'x-vidmob-user-role',
#     'x-vidmob-user-roles',
#     'x-vidmob-user-permissions',
#     'x-vidmob-user-permission-groups',
#     'x-vidmob-user-permission-group-ids',
#     'x-vidmob-user-permission-group-names',
#     'x-vidmob-user-permission-group-types',
#     'x-vidmob-user-permission-group-scopes',
#     'x-vidmob-user-permission-group-scope-ids',
#     'x-vidmob-user-permission-group-scope-names',
#     'x-vidmob-user-permission-group-scope-types',
#     'x-vidmob-user-permission-group-scope-values',
#     'x-vidmob-user-permission-group-scope-value-ids',
#     'x-vidmob-user-permission-group-scope-value-names',
#     'x-vidmob-user-permission-group-scope-value-types',
#     'x-vidmob-user-permission-group-scope-value-values',
#   ]

database:
  default:
    secret: dev/mysql/soa-rw